import firebase from 'firebase/compat/app';
import 'firebase/compat/auth';
import { Platform } from 'react-native';

// Import Firebase config from JSON file
import firebaseConfig from '../config/firebase-config.json';

// Initialize Firebase
if (!firebase.apps.length) {
  firebase.initializeApp(firebaseConfig);
} else {
  firebase.app(); // if already initialized, use that one
}

const auth = firebase.auth();

// Set persistence only on web platform (browser with localStorage)
if (Platform.OS === 'web' && typeof window !== 'undefined' && window.localStorage) {
  auth.setPersistence(firebase.auth.Auth.Persistence.LOCAL);
}

// Authentication functions
export const signIn = async (email: string, password: string) => {
  return await auth.signInWithEmailAndPassword(email, password);
};

export const signUp = async (email: string, password: string) => {
  return await auth.createUserWithEmailAndPassword(email, password);
};

export const logOut = async (): Promise<void> => {
  return await auth.signOut();
};

export { auth };

export const listenToAuthChanges = (callback: (user: firebase.User | null) => void) => {
  return auth.onAuthStateChanged(callback);
};
